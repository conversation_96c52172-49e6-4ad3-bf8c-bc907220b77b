// Content script to extract page content

// Listen for messages from the popup or background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "extractContent") {
    try {
      console.log("Content extraction requested for:", window.location.href);
      const pageContent = extractPageContent();
      console.log("Content extraction completed:", {
        title: pageContent.title,
        contentLength: pageContent.content.length,
        method: pageContent.extractionMethod
      });
      sendResponse({ content: pageContent });
    } catch (error) {
      console.error("Error extracting content:", error);
      sendResponse({
        content: {
          title: document.title || 'Error',
          url: window.location.href,
          content: `Error extracting content: ${error.message}`,
          extractionMethod: 'error'
        }
      });
    }
  }
  return true; // Required for async response
});

// Function to extract the main content from the page
function extractPageContent() {
  // Get the page title
  const title = document.title || 'Untitled Page';

  // Try to get the main content using multiple strategies
  let content = "";
  let extractionMethod = "";

  // Strategy 1: Look for article elements
  const articleElements = document.querySelectorAll('article');
  if (articleElements.length > 0) {
    content = articleElements[0].innerText;
    extractionMethod = "article";
  }

  // Strategy 2: Look for main element
  if (!content) {
    const mainElement = document.querySelector('main');
    if (mainElement) {
      content = mainElement.innerText;
      extractionMethod = "main";
    }
  }

  // Strategy 3: Look for common content containers
  if (!content) {
    const contentContainers = document.querySelectorAll('.content, .post, .entry, .article, #content, #main, .post-content, .entry-content, .article-content');
    if (contentContainers.length > 0) {
      content = contentContainers[0].innerText;
      extractionMethod = "content-container";
    }
  }

  // Strategy 4: Look for paragraph-rich areas
  if (!content) {
    const paragraphContainers = document.querySelectorAll('div');
    let bestContainer = null;
    let maxParagraphs = 0;

    paragraphContainers.forEach(container => {
      const paragraphs = container.querySelectorAll('p');
      if (paragraphs.length > maxParagraphs) {
        maxParagraphs = paragraphs.length;
        bestContainer = container;
      }
    });

    if (bestContainer && maxParagraphs > 2) {
      content = bestContainer.innerText;
      extractionMethod = "paragraph-rich";
    }
  }

  // Strategy 5: Fallback to body with cleanup
  if (!content) {
    const bodyClone = document.body.cloneNode(true);
    const elementsToRemove = bodyClone.querySelectorAll('script, style, nav, header, footer, aside, [role="banner"], [role="navigation"], [role="complementary"], .sidebar, .menu, .navigation, .ads, .advertisement');
    elementsToRemove.forEach(el => el.remove());
    content = bodyClone.innerText;
    extractionMethod = "body-fallback";
  }

  // Clean up the content (remove extra whitespace and limit length)
  content = content.replace(/\s+/g, ' ').trim();

  // Limit content length to avoid token limits (approximately 8000 characters)
  if (content.length > 8000) {
    content = content.substring(0, 8000) + "...";
  }

  // Ensure we have some content
  if (!content || content.length < 50) {
    content = `This page appears to have limited text content. Title: ${title}, URL: ${window.location.href}`;
  }

  console.log(`Content extracted using method: ${extractionMethod}, length: ${content.length}`);

  return {
    title,
    url: window.location.href,
    content,
    extractionMethod
  };
}
