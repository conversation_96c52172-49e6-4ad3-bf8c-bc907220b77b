// ---- Simple 24h cache for summaries (chrome.storage.local) ----
const SUMMARY_CACHE_KEY = 'summaryCache';
const SUMMARY_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours

function normalizeUrl(url) {
  try {
    const u = new URL(url);
    u.hash = '';
    return u.toString();
  } catch (e) {
    return (url || '').split('#')[0];
  }
}

async function getSummaryCache() {
  const obj = await chrome.storage.local.get(SUMMARY_CACHE_KEY);
  return obj[SUMMARY_CACHE_KEY] || {};
}

async function pruneExpiredSummaries(cache) {
  const now = Date.now();
  let mutated = false;
  for (const [key, value] of Object.entries(cache)) {
    if (!value || typeof value !== 'object' || !value.timestamp || (now - value.timestamp) > SUMMARY_TTL_MS) {
      delete cache[key];
      mutated = true;
    }
  }
  if (mutated) {
    await chrome.storage.local.set({ [SUMMARY_CACHE_KEY]: cache });
  }
}

async function getCachedSummary(url) {
  const norm = normalizeUrl(url);
  const cache = await getSummaryCache();
  const entry = cache[norm];
  const now = Date.now();
  if (entry && entry.summary && (now - entry.timestamp) < SUMMARY_TTL_MS) {
    return entry.summary;
  }
  // Expired or invalid; prune and return null
  await pruneExpiredSummaries(cache);
  return null;
}

async function setCachedSummary(url, summary) {
  const norm = normalizeUrl(url);
  const cache = await getSummaryCache();
  cache[norm] = { summary, timestamp: Date.now() };
  await pruneExpiredSummaries(cache);
  await chrome.storage.local.set({ [SUMMARY_CACHE_KEY]: cache });
}
// ----------------------------------------------------------------


// Function to fetch webpage content using Serper API
async function fetchWebpageContent(url, serperApiKey) {
  try {
    if (!serperApiKey) {
      throw new Error("Serper API key is required");
    }

    const headers = new Headers();
    headers.append("X-API-KEY", serperApiKey);
    headers.append("Content-Type", "application/json");

    const requestOptions = {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ url: url }),
      redirect: "follow"
    };

    const response = await fetch("https://scrape.serper.dev", requestOptions);

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.statusText}`);
    }

    const result = await response.text();
    return result;
  } catch (error) {
    console.error("Error fetching webpage content:", error);
    throw error;
  }
}

// Function to summarize content using OpenAI Chat Completions API
async function summarizeContent(page, apiKey, serperApiKey, model = 'gpt-4o-mini') {
  try {
    if (!apiKey) {
      throw new Error("OpenAI API key is required");
    }

    if (!serperApiKey) {
      throw new Error("Serper API key is required");
    }

    // Fetch webpage content using Serper
    console.log("Fetching webpage content using Serper API...");
    let webpageContent = '';
    try {
      webpageContent = await fetchWebpageContent(page.url, serperApiKey);
      // Limit content length to avoid token limits
      if (webpageContent.length > 8000) {
        webpageContent = webpageContent.substring(0, 8000) + '...';
      }
    } catch (serperError) {
      console.warn("Failed to fetch content with Serper, will use title and URL only:", serperError.message);
    }

    let prompt;
    if (webpageContent && webpageContent.trim()) {
      prompt = `Please provide a concise summary of the following webpage content in exactly 10 bullet points.

Page Title: ${page.title}
Page URL: ${page.url}

Webpage Content:
${webpageContent}

Instructions:
- Create exactly 10 bullet points (each starting with "•")
- Each bullet point should be concise but informative
- Focus on the main topics, key information, and important details
- Extract the most relevant information from the actual content`;
    } else {
      prompt = `Based on the page title and URL provided, please create a general summary of what this page likely contains in exactly 10 bullet points.

Page Title: ${page.title}
Page URL: ${page.url}

Instructions:
- Create exactly 10 bullet points (each starting with "•")
- Base your summary on what you can reasonably infer from the title and URL
- Be honest about limitations and indicate when you're making educated guesses
- Focus on likely topics and content areas this page would cover`;
    }

    // Check if it's a GPT-5 model (they don't support max_tokens and temperature)
    const isGPT5Model = model.startsWith('gpt-5');

    const requestBody = {
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      stream: true // Enable streaming
    };

    // Only add max_tokens and temperature for non-GPT-5 models
    if (!isGPT5Model) {
      requestBody.max_tokens = 1000;
      requestBody.temperature = 0.7;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      let errorData = {};
      try { errorData = await response.json(); } catch (e) { /* ignore */ }
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    // Handle streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullText = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                fullText += content;
                // Send streaming update to popup
                chrome.runtime.sendMessage({
                  action: 'streamUpdate',
                  content: fullText
                }).catch(() => {
                  // Ignore errors if popup is closed
                });
              }
            } catch (e) {
              // Ignore parsing errors for incomplete JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    if (!fullText || !fullText.trim()) {
      throw new Error('Empty response from OpenAI');
    }

    // Send completion message to popup
    chrome.runtime.sendMessage({
      action: 'streamComplete'
    }).catch(() => {
      // Ignore errors if popup is closed
    });

    return fullText.trim();
  } catch (error) {
    console.error("Error summarizing content:", error);
    throw error;
  }
}

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === "summarize") {
    handleSummarizeRequest(request.tabId, request.apiKey, request.serperApiKey, request.model)
      .then(summary => sendResponse({ success: true, summary }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Required for async response
  }
});

// Function to handle summarization requests
async function handleSummarizeRequest(tabId, apiKey, serperApiKey, model = 'gpt-4o-mini') {
  try {
    console.log("Starting summarization request for tab:", tabId);

    // Get the active tab title and URL only
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const page = { title: tab?.title || 'Untitled Page', url: tab?.url || '' };
    if (!page.url) {
      throw new Error('Unable to read active tab URL');
    }

    // Try cache first
    const cached = await getCachedSummary(page.url);
    if (cached) {
      console.log('Cache hit for', page.url);
      return cached;
    }

    // Summarize the page using Serper + OpenAI
    const summary = await summarizeContent(page, apiKey, serperApiKey, model);
    await setCachedSummary(page.url, summary);
    console.log("Summary generated successfully");

    return summary;
  } catch (error) {
    console.error("Error in handleSummarizeRequest:", error);
    throw error;
  }
}

// Old content extraction retained for potential fallback but unused
async function extractContentFromTab(_tabId) {
  return Promise.reject(new Error('Legacy content extraction is disabled.'));
}
