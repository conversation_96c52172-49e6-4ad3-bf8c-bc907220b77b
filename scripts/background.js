// User agents for randomization
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0'
];

// Function to get random user agent
function getRandomUserAgent() {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}
// ---- Simple 24h cache for summaries (chrome.storage.local) ----
const SUMMARY_CACHE_KEY = 'summaryCache';
const SUMMARY_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours

function normalizeUrl(url) {
  try {
    const u = new URL(url);
    u.hash = '';
    return u.toString();
  } catch (e) {
    return (url || '').split('#')[0];
  }
}

async function getSummaryCache() {
  const obj = await chrome.storage.local.get(SUMMARY_CACHE_KEY);
  return obj[SUMMARY_CACHE_KEY] || {};
}

async function pruneExpiredSummaries(cache) {
  const now = Date.now();
  let mutated = false;
  for (const [key, value] of Object.entries(cache)) {
    if (!value || typeof value !== 'object' || !value.timestamp || (now - value.timestamp) > SUMMARY_TTL_MS) {
      delete cache[key];
      mutated = true;
    }
  }
  if (mutated) {
    await chrome.storage.local.set({ [SUMMARY_CACHE_KEY]: cache });
  }
}

async function getCachedSummary(url) {
  const norm = normalizeUrl(url);
  const cache = await getSummaryCache();
  const entry = cache[norm];
  const now = Date.now();
  if (entry && entry.summary && (now - entry.timestamp) < SUMMARY_TTL_MS) {
    return entry.summary;
  }
  // Expired or invalid; prune and return null
  await pruneExpiredSummaries(cache);
  return null;
}

async function setCachedSummary(url, summary) {
  const norm = normalizeUrl(url);
  const cache = await getSummaryCache();
  cache[norm] = { summary, timestamp: Date.now() };
  await pruneExpiredSummaries(cache);
  await chrome.storage.local.set({ [SUMMARY_CACHE_KEY]: cache });
}
// ----------------------------------------------------------------


// Function to summarize a page using OpenAI Responses API with web_search tool
async function summarizeContent(page, apiKey) {
  try {
    if (!apiKey) {
      throw new Error("OpenAI API key is required");
    }

    const prompt = `You are a helpful assistant with access to a web_search tool.
Your task is to open and read the provided page URL and then produce a high‑quality summary.

Page Title: ${page.title}
Page URL: ${page.url}

Instructions:
- Use the web_search tool to access and read the exact page content at the URL above.
- Extract the main points from the actual page (do not hallucinate or rely on guesswork).
- Return exactly 10 concise bullet points (each starting with "•") that capture the key information from the page.
- If access fails or the page has little readable content, state that clearly and summarize whatever is available.`;

    const response = await fetch('https://api.openai.com/v1/responses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini',
        input: prompt,
        tools: [{ type: 'web_search' }],
        tool_choice: 'auto',
        temperature: 0.3,
        max_output_tokens: 1000
      })
    });

    if (!response.ok) {
      let errorData = {};
      try { errorData = await response.json(); } catch (e) { /* ignore */ }
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    // Prefer the Responses API aggregated field when available
    let text = '';
    if (typeof data.output_text === 'string' && data.output_text.trim()) {
      text = data.output_text;
    } else if (Array.isArray(data.output)) {
      text = data.output.map(part => Array.isArray(part.content)
        ? part.content.map(c => c.text || '').join('')
        : '').join('');
    } else if (data.choices && data.choices[0]?.message?.content) {
      // Fallback for Chat Completions-like responses
      text = data.choices[0].message.content;
    }

    if (!text || !text.trim()) {
      throw new Error('Empty response from OpenAI');
    }

    return text.trim();
  } catch (error) {
    console.error("Error summarizing content:", error);
    throw error;
  }
}

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === "summarize") {
    handleSummarizeRequest(request.tabId, request.apiKey)
      .then(summary => sendResponse({ success: true, summary }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Required for async response
  }
});

// Function to handle summarization requests
async function handleSummarizeRequest(tabId, apiKey) {
  try {
    console.log("Starting summarization request for tab:", tabId);

    // Get the active tab title and URL only
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const page = { title: tab?.title || 'Untitled Page', url: tab?.url || '' };
    if (!page.url) {
      throw new Error('Unable to read active tab URL');
    }

    // Try cache first
    const cached = await getCachedSummary(page.url);
    if (cached) {
      console.log('Cache hit for', page.url);
      return cached;
    }

    // Summarize the page via OpenAI web_search tool
    const summary = await summarizeContent(page, apiKey);
    await setCachedSummary(page.url, summary);
    console.log("Summary generated successfully");

    return summary;
  } catch (error) {
    console.error("Error in handleSummarizeRequest:", error);
    throw error;
  }
}

// Old content extraction retained for potential fallback but unused
async function extractContentFromTab(_tabId) {
  return Promise.reject(new Error('Legacy content extraction is disabled.'));
}
