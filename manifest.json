{"manifest_version": 3, "name": "<PERSON> Summarizer", "version": "2.0.0", "description": "Summarize web pages using OpenAI GPT-5 Nano + Serper web scraping", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["<all_urls>", "https://api.openai.com/*", "https://scrape.serper.dev/*"], "action": {"default_popup": "popup/popup.html", "default_icon": {"16": "icons/icon16.svg", "48": "icons/icon48.svg", "128": "icons/icon128.svg"}}, "background": {"service_worker": "scripts/background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["scripts/content.js"]}], "icons": {"16": "icons/icon16.svg", "48": "icons/icon48.svg", "128": "icons/icon128.svg"}}