/* Popup Styles */
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --background-color: #f8f9fa;
  --text-color: #212529;
  --border-color: #dee2e6;
  --success-color: #28a745;
  --error-color: #dc3545;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  width: 400px;
  min-height: 300px;
}

.container {
  padding: 16px;
}

header {
  margin-bottom: 16px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 12px;
}

h1 {
  font-size: 1.5rem;
  color: var(--primary-color);
}

h2 {
  font-size: 1.2rem;
  margin-bottom: 8px;
}

.section {
  margin-bottom: 16px;
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.form-group {
  display: flex;
  margin-bottom: 8px;
}

input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  width: 100%;
  padding: 12px;
  font-size: 16px;
}

.primary-btn:hover {
  background-color: #3a5bd9;
}

.secondary-btn {
  background-color: var(--secondary-color);
  color: white;
  margin-right: 8px;
}

.secondary-btn:hover {
  background-color: #5a6268;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group select {
  width: 100%;
  margin-bottom: 0;
}

.model-dropdown {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  color: var(--text-color);
}

#save-api-keys {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
}

.api-key-info {
  font-size: 12px;
  color: var(--secondary-color);
  margin-top: 8px;
}

.hidden {
  display: none !important;
}

#summary-content {
  margin: 12px 0;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  /* preserve bullet list newlines */
}

#summary-content.streaming {
  position: relative;
}

#summary-content.streaming::after {
  content: '▋';
  animation: blink 1s infinite;
  color: var(--primary-color);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.actions {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

.error-message {
  color: var(--error-color);
  margin-bottom: 12px;
}

footer {
  text-align: center;
  font-size: 12px;
  color: var(--secondary-color);
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

/* Loading spinner */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
}

.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}