// Popup script for the Page Summarizer extension

// DOM elements
const apiKeyForm = document.getElementById('api-key-form');
const apiKeyInput = document.getElementById('api-key-input');
const serperApiKeyInput = document.getElementById('serper-api-key-input');
const saveApiKeysBtn = document.getElementById('save-api-keys');
const summarizeBtn = document.getElementById('summarize-btn');
const loadingElement = document.getElementById('loading');
const summarySection = document.getElementById('summary-section');
const summaryContent = document.getElementById('summary-content');
const copyBtn = document.getElementById('copy-btn');
const newSummaryBtn = document.getElementById('new-summary-btn');
const errorSection = document.getElementById('error-section');
const errorText = document.getElementById('error-text');
const tryAgainBtn = document.getElementById('try-again-btn');

// Check if API keys exist in localStorage
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const { apiKey, serperApiKey } = await chrome.storage.local.get(['apiKey', 'serperApiKey']);
    if (apiKey && serperApiKey) {
      apiKeyForm.classList.add('hidden');
    } else {
      // Pre-fill existing keys if available
      if (apiKey) {
        apiKeyInput.value = apiKey;
      }
      if (serperApiKey) {
        serperApiKeyInput.value = serperApiKey;
      }
    }
  } catch (error) {
    console.error('Error checking API keys:', error);
  }
});

// Save API keys to localStorage
saveApiKeysBtn.addEventListener('click', async () => {
  const apiKey = apiKeyInput.value.trim();
  const serperApiKey = serperApiKeyInput.value.trim();

  if (!apiKey) {
    showError('Please enter a valid OpenAI API key');
    return;
  }

  if (!serperApiKey) {
    showError('Please enter a valid Serper API key');
    return;
  }

  try {
    await chrome.storage.local.set({ apiKey, serperApiKey });
    apiKeyForm.classList.add('hidden');
  } catch (error) {
    showError('Error saving API keys: ' + error.message);
  }
});

// Summarize button click handler
summarizeBtn.addEventListener('click', async () => {
  try {
    // Get the API keys from storage
    const { apiKey, serperApiKey } = await chrome.storage.local.get(['apiKey', 'serperApiKey']);

    if (!apiKey || !serperApiKey) {
      apiKeyForm.classList.remove('hidden');
      return;
    }

    // Show loading state
    showLoading(true);

    // Get the active tab (for title & URL only)
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // Send message to background script to summarize the page
    const response = await chrome.runtime.sendMessage({
      action: 'summarize',
      tabId: activeTab.id,
      apiKey,
      serperApiKey
    });

    // Hide loading state
    showLoading(false);

    if (response.success) {
      // Show the summary
      showSummary(response.summary);
    } else {
      // Show error
      showError(response.error || 'Failed to generate summary');
    }
  } catch (error) {
    showLoading(false);
    showError('Error: ' + error.message);
  }
});

// Copy summary to clipboard
copyBtn.addEventListener('click', () => {
  navigator.clipboard.writeText(summaryContent.innerText)
    .then(() => {
      copyBtn.textContent = 'Copied!';
      setTimeout(() => {
        copyBtn.textContent = 'Copy to Clipboard';
      }, 2000);
    })
    .catch(error => {
      showError('Failed to copy: ' + error.message);
    });
});

// New summary button click handler
newSummaryBtn.addEventListener('click', () => {
  resetUI();
});

// Try again button click handler
tryAgainBtn.addEventListener('click', () => {
  resetUI();
});

// Helper function to show loading state
function showLoading(isLoading) {
  if (isLoading) {
    loadingElement.classList.remove('hidden');
    summarizeBtn.classList.add('hidden');
  } else {
    loadingElement.classList.add('hidden');
    summarizeBtn.classList.remove('hidden');
  }
}

// Helper function to show summary
function showSummary(summary) {
  // Use textContent to avoid injecting HTML and rely on CSS to preserve newlines
  summaryContent.textContent = summary;
  summarySection.classList.remove('hidden');
  summarizeBtn.classList.add('hidden');
}

// Helper function to show error
function showError(message) {
  errorText.textContent = message;
  errorSection.classList.remove('hidden');
  summarizeBtn.classList.add('hidden');
}

// Helper function to reset UI
function resetUI() {
  summarySection.classList.add('hidden');
  errorSection.classList.add('hidden');
  summarizeBtn.classList.remove('hidden');
}
