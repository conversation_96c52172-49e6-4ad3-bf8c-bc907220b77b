<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Page Summarizer</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Page Summarizer</h1>
    </header>
    
    <div id="api-key-form" class="section">
      <h2>API Keys Setup</h2>
      <p>Enter your API keys to use the summarization feature.</p>

      <div class="form-group">
        <label for="api-key-input">OpenAI API Key:</label>
        <input type="password" id="api-key-input" placeholder="sk-...">
      </div>

      <div class="form-group">
        <label for="serper-api-key-input">Serper API Key:</label>
        <input type="password" id="serper-api-key-input" placeholder="Your Serper API key">
      </div>

      <button id="save-api-keys" class="primary-btn">Save API Keys</button>

      <div class="api-key-info">
        <p>Your API keys are stored locally and are only used to make requests to OpenAI and Serper.</p>
        <p><small>Get your Serper API key from <a href="https://serper.dev" target="_blank">serper.dev</a></small></p>
      </div>
    </div>
    
    <div id="summarize-section" class="section">
      <button id="summarize-btn" class="primary-btn">Summarize This Page</button>
      <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>Generating summary...</p>
      </div>
    </div>
    
    <div id="summary-section" class="section hidden">
      <h2>Summary</h2>
      <div id="summary-content"></div>
      <div class="actions">
        <button id="copy-btn" class="secondary-btn">Copy to Clipboard</button>
        <button id="new-summary-btn" class="secondary-btn">New Summary</button>
      </div>
    </div>
    
    <div id="error-section" class="section hidden">
      <div class="error-message">
        <p id="error-text"></p>
      </div>
      <button id="try-again-btn" class="secondary-btn">Try Again</button>
    </div>
    
    <footer>
      <p>Powered by OpenAI GPT-4o Mini + Serper</p>
    </footer>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
